# 🛑 Ignorar dependências do Node.js (Frontend)
node_modules/

# 🛑 Ignorar builds e ficheiros compilados
build/
dist/

# 🛑 Ignorar dependências do Python (Backend)
venv/

# 🛑 Ignorar ficheiros de logs e temporários
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log

# 🛑 Ignorar ficheiros de ambiente
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 🛑 Ignorar ficheiros do sistema
.DS_Store
Thumbs.db

# Ignorar ficheiros compilados pelo Python
__pycache__/
backend/app/routes/__pycache__/
backend/app/middleware/__pycache__/
*.pyc
*.pyo

# 🛑 Ignorar uploads de usuários mas manter a estrutura
backend/uploads/profile_images/user_uploads/*
!backend/uploads/profile_images/user_uploads/.gitkeep

# 🛑 Ignorar ficheiros de script locais
start_app.sh
stop_app.sh
app_pids.sh
kill_ports.bat
start_app_linux.py
start_app_fedora.sh
stop_app_fedora.sh
app_pids.txt
*.deps_installed

# Ignorar logs gerados pelos scripts
backend.log
frontend.log
