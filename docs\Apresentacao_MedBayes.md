# Apresentação MedBayes V2

## Estrutura dos Slides

1. Introdução e Boas-vindas
2. O que é o MedBayes?
3. Problema e Motivação
4. Visão Geral da Solução
5. Arquitetura e Tecnologias
6. Demonstração: Fluxo do Utilizador
7. Funcionalidades-Chave
8. Demonstração: Diagnóstico Bayesiano
9. Segurança e Conformidade
10. Resultados e Impacto
11. Próximos Passos
12. Q&A / Encerramento

---

## Texto para Slides (ppt)

### 1. Introdução e Boas-vindas
- Bem-vindos à apresentação do MedBayes
- Otimização do fluxo de doentes e apoio ao diagnóstico

### 2. O que é o MedBayes?
- Plataforma integrada para gestão hospitalar
- Triagem Manchester + Diagnóstico Bayesiano
- Centralização e eficiência

### 3. Problema e Motivação
- Tempos de espera elevados
- Risco de erro no diagnóstico
- Necessidade de gestão centralizada

### 4. Visão Geral da Solução
- Fluxo digital do paciente: receção → triagem → consulta → internamento
- Algoritmo Bayesiano para apoio à decisão médica
- Dashboards e relatórios em tempo real

### 5. Arquitetura e Tecnologias
- Frontend: React 18+
- Backend: Python (Flask)
- Base de dados: MySQL
- APIs RESTful, autenticação JWT

### 9. Segurança e Conformidade
- Autenticação JWT
- Proteção de dados (RGPD)
- Registo de auditoria


### 6. Demonstração: Fluxo do Utilizador
- Registo de paciente
- Triagem Manchester
- Consulta médica
- Internamento e alta

### 7. Funcionalidades-Chave
- Diagnóstico assistido por IA
- Gestão de internamentos
- Relatórios e estatísticas
- Notificações em tempo real
- Exportação de dados (PDF, CSV)

### 8. Demonstração: Diagnóstico Bayesiano
- Seleção de sintomas
- Cálculo de probabilidades
- Sugestão de diagnóstico



### 10. Resultados e Impacto
- Redução de tempos de espera
- Apoio à decisão clínica
- Melhoria da gestão hospitalar

### 11. Próximos Passos
- Integração com dispositivos médicos
- Acessibilidade e multilingue
- Backup e recuperação

### 12. Q&A / Encerramento
- Obrigado pela atenção!
- Perguntas e demonstrações adicionais

---

## Guião Oral para 2 Apresentadores

> **Sugestão:** Apresentador 1 (Alexandre), Apresentador 2 (Eduardo). Troquem de slide e de orador para manter ritmo. Demonstrações podem ser feitas em partilha de ecrã.

### Slide 1: Introdução e Boas-vindas
**Alexandre:**
"Boa tarde a todos, sejam bem-vindos à apresentação do MedBayes. O meu nome é Alexandre Vicente e comigo está o/a Eduardo Costa. Vamos mostrar como o MedBayes pode transformar a gestão hospitalar, otimizando o fluxo de doentes e apoiando o diagnóstico médico."

**Eduardo:**
"Durante esta apresentação, vamos explicar o problema, mostrar a nossa solução, e fazer demonstrações práticas das principais funcionalidades."

---

### Slide 2: O que é o MedBayes?
**Eduardo:**
"O MedBayes é uma plataforma digital integrada para hospitais e clínicas. Junta a triagem pelo Protocolo de Manchester com um sistema de diagnóstico assistido por IA, baseado no Teorema de Bayes. Tudo isto numa solução centralizada, moderna e eficiente."

---

### Slide 3: Problema e Motivação
**Alexandre:**
"O nosso ponto de partida foram três grandes desafios: tempos de espera elevados, risco de erro no diagnóstico e a necessidade de centralizar toda a informação clínica e operacional."

**Eduardo:**
"Estes problemas afetam não só os profissionais de saúde, mas também a experiência e segurança dos pacientes."

---

### Slide 4: Visão Geral da Solução
**Eduardo:**
"A nossa solução digitaliza todo o percurso do paciente: desde a receção, passando pela triagem, consulta médica, até ao internamento e alta."

**Alexandre:**
"O algoritmo bayesiano apoia o médico na decisão, e os dashboards permitem monitorizar tudo em tempo real."

---

### Slide 5: Arquitetura e Tecnologias
**Alexandre:**
"O MedBayes assenta numa arquitetura moderna: frontend em React, backend em Python Flask, base de dados MySQL, e comunicação via APIs RESTful."

**Eduardo:**
"A segurança é garantida por autenticação JWT e todas as comunicações são protegidas."

---

### Slide 6: Demonstração: Fluxo do Utilizador
**Eduardo:**
"Vamos agora mostrar o fluxo típico de um paciente. [Partilha de ecrã]"

**Alexandre:**
"Começamos pelo registo do paciente, depois a triagem Manchester, consulta médica, e se necessário, internamento e alta."

*(Demonstração ao vivo: cada passo explicado brevemente enquanto navegam na aplicação)*

---

### Slide 7: Funcionalidades-Chave
**Alexandre:**
"O MedBayes oferece diagnóstico assistido por IA, gestão de internamentos, relatórios detalhados, notificações em tempo real e exportação de dados."

**Eduardo:**
"Tudo isto numa interface intuitiva, acessível e preparada para crescer."

---

### Slide 8: Demonstração: Diagnóstico Bayesiano
**Eduardo:**
"Vamos agora demonstrar o diagnóstico assistido. [Partilha de ecrã]"

**Alexandre:**
"Selecionamos sintomas, o sistema calcula probabilidades e sugere diagnósticos, sempre com transparência e controlo pelo médico."

*(Demonstração ao vivo: mostrar seleção de sintomas e resultado bayesiano)*

---

### Slide 9: Segurança e Conformidade
**Alexandre:**
"A segurança dos dados é uma prioridade: usamos autenticação JWT, cumprimos o RGPD e mantemos registo de todas as ações clínicas para auditoria."

---

### Slide 10: Resultados e Impacto
**Eduardo:**
"Com o MedBayes, reduzimos tempos de espera, apoiamos a decisão clínica e melhoramos a gestão hospitalar."

**Alexandre:**
"O feedback dos utilizadores tem sido muito positivo, destacando a facilidade de uso e o impacto real na prática clínica."

---

### Slide 11: Próximos Passos
**Alexandre:**
"Os próximos passos incluem integração com dispositivos médicos, acessibilidade reforçada e suporte multilingue."

**Eduardo:**
"Estamos também a preparar funcionalidades de backup e recuperação para garantir máxima fiabilidade."

---

### Slide 12: Q&A / Encerramento
**Eduardo:**
"Agradecemos a vossa atenção. Estamos disponíveis para perguntas e para demonstrar qualquer funcionalidade que queiram ver em detalhe."

**Alexandre:**
"Muito obrigado!" 