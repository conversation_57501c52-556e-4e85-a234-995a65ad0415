{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "animate.css": "^4.1.1", "axios": "^1.8.4", "chart.js": "^4.4.8", "date-fns": "^2.30.0", "react": "^18.2.0", "react-datepicker": "^4.16.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-router-dom": "^6.30.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^7.1.0"}}