import React, { useState, useEffect } from "react";
import Notification from "../common/Notification";
import api from "../../services/api";
import {
  validarSintomas,
  formatarErrosValidacao,
} from "../../utils/validations";

const Step2 = ({
  sintomaSelecionado,
  setSintomaSelecionado,
  intensidadeSintoma,
  setIntensidadeSintoma,
  duracaoSintoma,
  setDuracaoSintoma,
  formData,
  setFormData,
  setStep,
}) => {
  const [notifications, setNotifications] = useState([]);
  const [listaSintomas, setListaSintomas] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  // Variáveis removidas pois não são utilizadas
  // const [inputValue, setInputValue] = useState("");

  useEffect(() => {
    const carregarSintomas = async () => {
      try {
        const response = await api.get("/sintomas");
        setListaSintomas(response.data);
      } catch (error) {
        console.error("Erro ao carregar sintomas:", error);
        setNotifications((prev) => [
          ...prev,
          {
            id: Date.now(),
            message: "Erro ao carregar lista de sintomas",
            type: "error",
          },
        ]);
      }
    };

    carregarSintomas();
  }, []);

  const handleSintomaSelecionado = (sintoma) => {
    setSintomaSelecionado(sintoma);
    // Pequeno delay antes de esconder as sugestões
    setTimeout(() => {
      setShowSuggestions(false);
    }, 100);
  };

  const adicionarSintoma = () => {
    if (sintomaSelecionado) {
      // Encontrar o id_sintoma correspondente ao sintoma selecionado
      const sintomaSelecionadoObj = listaSintomas.find(
        (s) => s.sintoma === sintomaSelecionado
      );

      if (!sintomaSelecionadoObj) {
        setNotifications((prev) => [
          ...prev,
          {
            id: Date.now(),
            message: "Sintoma não encontrado na lista!",
            type: "error",
          },
        ]);
        return;
      }

      const novoSintoma = {
        id_sintoma: sintomaSelecionadoObj.id_sintoma,
        sintoma: sintomaSelecionado,
        intensidade: parseInt(intensidadeSintoma || "1", 10),
        duracao_sintomas: parseInt(duracaoSintoma || "1", 10),
      };

      setFormData((prev) => ({
        ...prev,
        sintomas: [...prev.sintomas, novoSintoma],
      }));

      console.log(" Sintoma adicionado ao formData:", novoSintoma);

      setNotifications((prev) => [
        ...prev,
        {
          id: Date.now(),
          message: "Sintoma registado com sucesso!",
          type: "success",
        },
      ]);

      // Limpar campos após adicionar
      setSintomaSelecionado("");
      setIntensidadeSintoma("");
      setDuracaoSintoma("");
    } else {
      setNotifications((prev) => [
        ...prev,
        {
          id: Date.now(),
          message: "Selecione um sintoma para adicionar!",
          type: "error",
        },
      ]);
    }
  };

  // Função para remover um sintoma
  const removerSintoma = (index) => {
    setFormData((prev) => ({
      ...prev,
      sintomas: prev.sintomas.filter((_, i) => i !== index),
    }));

    setNotifications((prev) => [
      ...prev,
      {
        id: Date.now(),
        message: "Sintoma removido com sucesso!",
        type: "info",
      },
    ]);
  };

  // Função para gerar dados aleatórios de sintomas
  const gerarDadosRandom = () => {
    if (!listaSintomas || listaSintomas.length === 0) {
      setNotifications((prev) => [
        ...prev,
        { id: Date.now(), message: "Lista de sintomas vazia!", type: "error" },
      ]);
      return;
    }

    // Seleciona um sintoma aleatório da lista
    const sintomaEscolhido =
      listaSintomas[Math.floor(Math.random() * listaSintomas.length)];

    const novoSintoma = {
      id_sintoma: sintomaEscolhido.id_sintoma,
      sintoma: sintomaEscolhido.sintoma,
      intensidade: (Math.floor(Math.random() * 10) + 1).toString(),
      duracao_sintomas: (Math.floor(Math.random() * 14) + 1).toString(),
    };

    console.log(
      " Sintoma Gerado:",
      novoSintoma.sintoma,
      "ID:",
      novoSintoma.id_sintoma,
      "Intensidade:",
      novoSintoma.intensidade,
      "Duração:",
      novoSintoma.duracao_sintomas
    );

    if (setFormData) {
      setFormData((prev) => ({
        ...prev,
        sintomas: [...(prev.sintomas || []), novoSintoma],
      }));
    }
  };

  const atualizarListaSintomas = async () => {
    try {
      const response = await api.get("/sintomas");
      setListaSintomas(response.data);
      setNotifications((prev) => [
        ...prev,
        {
          id: Date.now(),
          message: "Lista de sintomas atualizada!",
          type: "success",
        },
      ]);
    } catch (error) {
      console.error(" Erro ao atualizar sintomas:", error);
      setNotifications((prev) => [
        ...prev,
        {
          id: Date.now(),
          message: "Erro ao atualizar lista de sintomas",
          type: "error",
        },
      ]);
    }
  };

  useEffect(() => {
    if (notifications.length > 0) {
      const timer = setTimeout(() => {
        setNotifications([]);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [notifications]);

  return (
    <div className="step-container">
      <div className="notifications-container">
        {notifications.map((notif) => (
          <Notification
            key={notif.id}
            message={notif.message}
            type={notif.type}
          />
        ))}
      </div>
      <label>Introduza os Sintomas:</label>
      <div style={{ position: "relative" }}>
        <input
          type="text"
          value={sintomaSelecionado}
          onChange={(e) => {
            setSintomaSelecionado(e.target.value);
            setShowSuggestions(true);
          }}
          onFocus={() => {
            setShowSuggestions(true);
          }}
          onBlur={(e) => {
            // Aumentar o delay para permitir o clique nas sugestões
            setTimeout(() => setShowSuggestions(false), 300);
          }}
          placeholder="Digite um sintoma..."
          style={{
            padding: "12px 15px",
            borderRadius: "8px",
            border: "1px solid #e2e8f0",
            width: "100%",
            marginBottom: "15px",
            fontSize: "16px",
          }}
        />

        {showSuggestions && (
          <div
            className="suggestions-list"
            style={{
              position: "absolute",
              top: "100%",
              left: 0,
              right: 0,
              maxHeight: "200px",
              overflowY: "auto",
              backgroundColor: "white",
              border: "1px solid #e2e8f0",
              borderRadius: "8px",
              zIndex: 1000,
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
              marginTop: "-10px",
            }}
          >
            {listaSintomas
              .filter(
                (s) =>
                  !sintomaSelecionado ||
                  s.sintoma
                    .toLowerCase()
                    .includes(sintomaSelecionado.toLowerCase())
              )
              .sort((a, b) => a.sintoma.localeCompare(b.sintoma, "pt-PT"))
              .map((s) => (
                <div
                  key={s.id_sintoma}
                  onClick={() => handleSintomaSelecionado(s.sintoma)}
                  style={{
                    padding: "10px 15px",
                    cursor: "pointer",
                    borderBottom: "1px solid #e2e8f0",
                    transition: "background-color 0.2s",
                  }}
                  onMouseEnter={(e) =>
                    (e.target.style.backgroundColor = "#f7fafc")
                  }
                  onMouseLeave={(e) =>
                    (e.target.style.backgroundColor = "white")
                  }
                >
                  {s.sintoma}
                </div>
              ))}
          </div>
        )}
      </div>
      {sintomaSelecionado && (
        <>
          <label>Intensidade (1-10): {intensidadeSintoma}</label>
          <input
            type="range"
            min="1"
            max="10"
            value={intensidadeSintoma || "1"}
            onChange={(e) =>
              setIntensidadeSintoma && setIntensidadeSintoma(e.target.value)
            }
            style={{
              width: "100%",
              height: "40px",
              marginBottom: "20px",
              cursor: "pointer",
              accentColor: "#16808C",
            }}
          />

          <label>Duração (em dias): {duracaoSintoma}</label>
          <input
            type="range"
            min="1"
            max="15"
            value={duracaoSintoma || "1"}
            onChange={(e) =>
              setDuracaoSintoma && setDuracaoSintoma(e.target.value)
            }
            style={{
              width: "100%",
              height: "40px",
              marginBottom: "20px",
              cursor: "pointer",
              accentColor: "#16808C",
            }}
          />

          <button
            onClick={adicionarSintoma}
            style={{
              backgroundColor: "#16808C",
              color: "white",
              padding: "10px 20px",
              border: "none",
              borderRadius: "8px",
              cursor: "pointer",
              width: "100%",
              marginBottom: "20px",
              fontSize: "16px",
              fontWeight: "bold",
              transition: "background-color 0.2s",
            }}
          >
            Adicionar Sintoma
          </button>
        </>
      )}
      <div className="symptoms-list-container">
        <ul>
          {formData.sintomas.map((s, index) => (
            <li key={index}>
              {s.sintoma} - Intensidade: {s.intensidade}, Duração:{" "}
              {s.duracao_sintomas} dias
              <button
                onClick={() => removerSintoma(index)}
                style={{
                  marginLeft: "10px",
                  backgroundColor: "red",
                  color: "white",
                  border: "none",
                  cursor: "pointer",
                  padding: "5px 10px",
                }}
              >
                Remover
              </button>
            </li>
          ))}
        </ul>
      </div>

      <div className="action-buttons-centered">
        <button className="secondary" onClick={() => setStep(1)}>
          Voltar
        </button>
        <button
          className="primary"
          onClick={() => {
            // Validar sintomas antes de prosseguir
            if (formData.sintomas.length === 0) {
              setNotifications((prev) => [
                ...prev,
                {
                  id: Date.now(),
                  message:
                    "Por favor, adicione pelo menos um sintoma antes de prosseguir.",
                  type: "error",
                },
              ]);
              return;
            }

            // Converter sintomas para o formato esperado pela função de validação
            const sintomasParaValidar = formData.sintomas.map((s) => ({
              id_sintoma: s.id_sintoma,
              intensidade: parseInt(s.intensidade),
              duracao_sintomas: parseInt(s.duracao_sintomas),
            }));

            const { isValid, errors } = validarSintomas(sintomasParaValidar);

            if (!isValid) {
              setNotifications((prev) => [
                ...prev,
                {
                  id: Date.now(),
                  message: formatarErrosValidacao(errors),
                  type: "error",
                },
              ]);
              return;
            }

            setStep(3);
          }}
        >
          Próximo
        </button>
      </div>

      <div className="debug-buttons">
        <button className="debug-button" onClick={gerarDadosRandom}>
          Gerar Sintoma
        </button>
        <button className="debug-button" onClick={atualizarListaSintomas}>
          Atualizar Lista de Sintomas
        </button>
      </div>
    </div>
  );
};

export default Step2;
