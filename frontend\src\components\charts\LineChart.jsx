import React, { useEffect, useRef } from "react";
import Chart from "chart.js/auto";

const LineChart = ({ data, title, xAxisLabel, yAxisLabel }) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    if (chartRef.current) {
      // Destruir o gráfico anterior se existir
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }

      const ctx = chartRef.current.getContext("2d");
      
      chartInstance.current = new Chart(ctx, {
        type: "line",
        data: data,
        options: {
          responsive: true,
          maintainAspectRatio: true,
          plugins: {
            title: {
              display: title ? true : false,
              text: title || "",
              font: {
                size: 16,
              },
            },
            legend: {
              position: "top",
              display: true,
            },
          },
          scales: {
            x: {
              title: {
                display: true,
                text: xAxisLabel || "",
              },
            },
            y: {
              title: {
                display: true,
                text: yAxisLabel || "",
              },
              beginAtZero: true,
            },
          },
        },
      });
    }

    // Cleanup function
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [data, title, xAxisLabel, yAxisLabel]);

  return <canvas ref={chartRef}></canvas>;
};

export default LineChart;
