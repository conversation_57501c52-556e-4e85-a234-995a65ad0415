import React, { useEffect, useRef } from "react";
import Chart from "chart.js/auto";

const PieChart = ({ data, title, colors }) => {
  const chartRef = useRef(null);
  const chartInstance = useRef(null);

  useEffect(() => {
    if (chartRef.current) {
      // Destruir o gráfico anterior se existir
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }

      const ctx = chartRef.current.getContext("2d");
      
      // Aplicar cores personalizadas se fornecidas
      if (colors && data.datasets && data.datasets.length > 0) {
        data.datasets[0].backgroundColor = colors;
      }
      
      chartInstance.current = new Chart(ctx, {
        type: "pie",
        data: data,
        options: {
          responsive: true,
          maintainAspectRatio: true, // Garantir proporção correta
          aspectRatio: 1, // Forçar proporção 1:1 para círculo perfeito
          plugins: {
            title: {
              display: title ? true : false,
              text: title || "",
              font: {
                size: 16,
              },
              padding: {
                top: 10,
                bottom: 20
              }
            },
            legend: {
              position: "bottom", // Posicionar legenda abaixo para mais espaço para o gráfico
              display: true,
              labels: {
                boxWidth: 15,
                padding: 15,
                font: {
                  size: 12
                }
              }
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const label = context.label || '';
                  const value = context.formattedValue;
                  const dataset = context.dataset;
                  const total = dataset.data.reduce((acc, data) => acc + data, 0);
                  const percentage = Math.round((context.raw / total) * 100);
                  return `${label}: ${value} (${percentage}%)`;
                }
              }
            }
          },
          layout: {
            padding: {
              left: 10,
              right: 10,
              top: 10,
              bottom: 10
            }
          },
          cutout: '0%' // Garantir que é um gráfico de pizza completo
        },
      });
    }

    // Cleanup function
    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [data, title, colors]);

  // Usar uma div com proporção fixa para garantir um círculo perfeito
  return (
    <div className="pie-chart-container">
      <canvas ref={chartRef}></canvas>
    </div>
  );
};

export default PieChart;
