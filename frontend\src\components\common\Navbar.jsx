import React from "react";
import { useNavigate } from "react-router-dom";
import "../../styles/navbar.css";
import logo from "../../assets/logo1.png";

const Navbar = () => {
  const navigate = useNavigate();

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <nav className="navbar">
      <div className="navbar-content">
        <div className="navbar-left">
          <div
            onClick={scrollToTop}
            className="navbar-brand"
            style={{ cursor: "pointer" }}
          >
            <img src={logo} alt="MedBayes" className="navbar-logo" />
          </div>
        </div>
        <div className="navbar-right">
          <button
            onClick={() => navigate("/documents")}
            className="secondary-button"
          >
            Documentos
          </button>
          <button onClick={() => navigate("/login")} className="primary-button">
            Começar Agora
          </button>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
