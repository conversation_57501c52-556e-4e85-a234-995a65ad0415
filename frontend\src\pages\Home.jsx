import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import "../styles/home.css";
import Navbar from "../components/common/Navbar";
import Footer from "../components/common/Footer";
import 'animate.css/animate.min.css';
import { FaStethoscope, FaUserMd, FaHospital, FaClock, FaBrain, FaChartLine, FaHeartbeat, FaChartBar, FaShieldAlt, FaNotesMedical } from 'react-icons/fa';

// Import your images
import heroImage1 from '../assets/hero-bg.jpg';
import heroImage2 from '../assets/hospital-modern.jpg'; // You'll need to add these images
import heroImage3 from '../assets/ai-medical.jpg';
import heroImage4 from '../assets/doctor-patient.jpg';

const Home = () => {
  const navigate = useNavigate();
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      image: heroImage1,
      title: "MedBayes",
      subtitle: "Inteligência Artificial para Triagem Médica",
      description: "Sistema avançado de triagem e diagnóstico médico utilizando inteligência artificial para otimizar o atendimento em saúde"
    },
    {
      image: heroImage2,
      title: "Tecnologia Avançada",
      subtitle: "Diagnóstico Preciso e Rápido",
      description: "Utilizando algoritmos de última geração para garantir a melhor precisão no diagnóstico"
    },
    {
      image: heroImage3,
      title: "Inteligência Artificial",
      subtitle: "Machine Learning em Saúde",
      description: "Análise preditiva e aprendizado contínuo para melhorar os resultados"
    },
    {
      image: heroImage4,
      title: "Cuidado Humanizado",
      subtitle: "Tecnologia a Serviço da Saúde",
      description: "Combinando inovação tecnológica com atendimento humanizado"
    }
  ];

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prevSlide) => (prevSlide + 1) % slides.length);
    }, 5000);

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="home-container">
      <Navbar />
      
      {/* Hero Section with Slideshow */}
      <section className="hero-section">
        <div className="slideshow-container">
          {slides.map((slide, index) => (
            <div 
              key={index} 
              className={`slide ${index === currentSlide ? 'active' : ''}`}
              style={{ backgroundImage: `url(${slide.image})` }}
            >
              {index === currentSlide && (
                <div className="hero-content">
                  <h1 className="animate__animated animate__fadeInDown animate__delay-0.5s">
                    {slide.title}
                  </h1>
                  <h2 className="animate__animated animate__fadeInUp animate__delay-0.7s">
                    {slide.subtitle}
                  </h2>
                  <p className="animate__animated animate__fadeIn animate__delay-1s">
                    {slide.description}
                  </p>
                </div>
              )}
            </div>
          ))}
          
          {/* Navigation Dots */}
          <div className="slide-dots">
            {slides.map((_, index) => (
              <button
                key={index}
                className={`slide-dot ${index === currentSlide ? 'active' : ''}`}
                onClick={() => goToSlide(index)}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features-section">
        <h2 className="section-title animate__animated animate__fadeIn">
          Recursos Principais
        </h2>
        <div className="features-grid">
          <div className="feature-card animate__animated animate__fadeInUp">
            <FaStethoscope className="feature-icon" />
            <h3>Triagem Inteligente</h3>
            <p>Algoritmos avançados para classificação precisa de pacientes</p>
          </div>

          <div className="feature-card animate__animated animate__fadeInUp">
            <FaBrain className="feature-icon" />
            <h3>IA Diagnóstica</h3>
            <p>Suporte ao diagnóstico baseado em machine learning</p>
          </div>

          <div className="feature-card animate__animated animate__fadeInUp">
            <FaClock className="feature-icon" />
            <h3>Tempo Real</h3>
            <p>Monitoramento e atualização em tempo real dos casos</p>
          </div>

          <div className="feature-card animate__animated animate__fadeInUp">
            <FaChartLine className="feature-icon" />
            <h3>Analytics</h3>
            <p>Análise detalhada e relatórios estatísticos</p>
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="statistics-section">
        <div className="stat-container animate__animated animate__fadeIn">
          <div className="stat-item">
            <h3>98%</h3>
            <p>Precisão na Triagem</p>
          </div>
          <div className="stat-item">
            <h3>-45%</h3>
            <p>Tempo de Espera</p>
          </div>
          <div className="stat-item">
            <h3>+10k</h3>
            <p>Pacientes Atendidos</p>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="benefits-section">
        <h2 className="section-title animate__animated animate__fadeIn">
          Benefícios do MedBayes
        </h2>
        <div className="benefits-grid">
          <div className="benefit-card animate__animated animate__fadeInUp">
            <div className="benefit-icon">
              <FaChartLine />
            </div>
            <h3>Maior Eficiência</h3>
            <p>Redução de até 45% no tempo de espera e otimização do fluxo de pacientes</p>
          </div>

          <div className="benefit-card animate__animated animate__fadeInUp">
            <div className="benefit-icon">
              <FaStethoscope />
            </div>
            <h3>Precisão Diagnóstica</h3>
            <p>98% de precisão na triagem com suporte de inteligência artificial</p>
          </div>

          <div className="benefit-card animate__animated animate__fadeInUp">
            <div className="benefit-icon">
              <FaUserMd />
            </div>
            <h3>Suporte à Decisão</h3>
            <p>Auxílio baseado em evidências para tomada de decisão clínica</p>
          </div>

          <div className="benefit-card animate__animated animate__fadeInUp">
            <div className="benefit-icon">
              <FaHospital />
            </div>
            <h3>Gestão Integrada</h3>
            <p>Sistema completo de gestão hospitalar com módulos integrados</p>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="how-it-works-section">
        <h2 className="section-title animate__animated animate__fadeIn">
          Como o MedBayes Funciona
        </h2>
        <div className="workflow-container">
          <div className="workflow-grid">
            <div className="workflow-card animate__animated animate__fadeInUp">
              <div className="workflow-icon">
                <FaUserMd />
              </div>
              <h3>Entrada do Paciente</h3>
              <p>Registro inicial com coleta de dados vitais e sintomas principais. Nossa equipe realiza a primeira avaliação.</p>
            </div>

            <div className="workflow-card animate__animated animate__fadeInUp">
              <div className="workflow-icon">
                <FaBrain />
              </div>
              <h3>Análise por IA</h3>
              <p>Processamento dos dados usando algoritmos avançados de machine learning para análise precisa.</p>
            </div>

            <div className="workflow-card animate__animated animate__fadeInUp">
              <div className="workflow-icon">
                <FaStethoscope />
              </div>
              <h3>Triagem Inteligente</h3>
              <p>Classificação automática baseada no Protocolo Manchester com suporte de IA.</p>
            </div>

            <div className="workflow-card animate__animated animate__fadeInUp">
              <div className="workflow-icon">
                <FaNotesMedical />
              </div>
              <h3>Resposta Médica</h3>
              <p>Diagnóstico final e plano de tratamento definido pelo médico com suporte das recomendações do sistema.</p>
            </div>
          </div>
        </div>
      </section>

      {/* More Information Section */}
      <section className="more-info-section">
        <h2 className="section-title animate__animated animate__fadeIn">
          Mais Informações
        </h2>
        <div className="info-container">
          <div className="info-grid">
            <div className="info-card animate__animated animate__fadeInUp">
              <div className="info-icon">
                <FaHeartbeat />
              </div>
              <h3>Monitoramento em Tempo Real</h3>
              <ul className="info-list">
                <li>Dashboard atualizado em tempo real</li>
                <li>Alertas automáticos de sinais vitais</li>
                <li>Acompanhamento contínuo de pacientes</li>
                <li>Notificações instantâneas</li>
              </ul>
              <button className="info-button">Saiba Mais</button>
            </div>

            <div className="info-card animate__animated animate__fadeInUp">
              <div className="info-icon">
                <FaUserMd />
              </div>
              <h3>Suporte Médico</h3>
              <ul className="info-list">
                <li>Equipe especializada</li>
                <li>Consultoria 24/7</li>
                <li>Treinamento contínuo</li>
                <li>Protocolos atualizados</li>
              </ul>
              <button className="info-button">Fale Conosco</button>
            </div>

            <div className="info-card animate__animated animate__fadeInUp">
              <div className="info-icon">
                <FaShieldAlt />
              </div>
              <h3>Segurança e Privacidade</h3>
              <ul className="info-list">
                <li>Dados criptografados</li>
                <li>Conformidade com LGPD</li>
                <li>Backups automáticos</li>
                <li>Controle de acesso</li>
              </ul>
              <button className="info-button">Ler Política</button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Home;
