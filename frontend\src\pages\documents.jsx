import React, { useState } from 'react';
import "../styles/documents.css";

const AccordionItem = ({ title, children, isOpen, onClick }) => {
  return (
    <div className="accordion-item">
      <div className={`accordion-header ${isOpen ? 'active' : ''}`} onClick={onClick}>
        <h2>{title}</h2>
        <span className="accordion-icon">{isOpen ? '−' : '+'}</span>
      </div>
      {isOpen && (
        <div className="accordion-content">
          {children}
        </div>
      )}
    </div>
  );
};

const Documents = () => {
  const [openSection, setOpenSection] = useState(null);

  const toggleSection = (index) => {
    setOpenSection(openSection === index ? null : index);
  };

  return (
    <div className="page-container">
      <div className="documents-container">
        <header className="documents-header">
          <h1>Documentação MedBayes V2</h1>
        </header>

        <div className="accordion">
          <AccordionItem 
            title="Visão Geral do Projeto" 
            isOpen={openSection === 0}
            onClick={() => toggleSection(0)}
          >
            <h3>Tecnologias Principais</h3>
            <ul>
              <li>Backend: Python 3.11+</li>
              <li>Frontend: React 18+</li>
              <li>Base de Dados: MySQL 8.0+</li>
              <li>Documentação: Markdown/MDX</li>
            </ul>

            <h3>Objetivos Estratégicos</h3>
            <ul>
              <li>Otimização do fluxo de doentes através do Protocolo de Manchester</li>
              <li>Auxílio ao diagnóstico médico usando o Teorema de Bayes</li>
              <li>Gestão hospitalar centralizada e eficiente</li>
              <li>Redução de erros diagnósticos e tempos de espera</li>
            </ul>
          </AccordionItem>

          <AccordionItem 
            title="Estrutura do Projeto" 
            isOpen={openSection === 1}
            onClick={() => toggleSection(1)}
          >
            <h3>Organização de Diretórios</h3>
            <pre>
              {`medbayesV2/
├── backend/             # Código do backend em Python/Flask
│   ├── app/             # Aplicação principal
│   │   ├── middleware/  # Middlewares (autenticação, etc.)
│   │   ├── models/      # Modelos de dados
│   │   ├── routes/      # Rotas da API
│   │   └── services/    # Serviços de negócio
│   ├── scripts/         # Scripts de manutenção e configuração
│   └── tests/           # Testes unitários e de integração
├── BD/                  # Scripts SQL e diagramas do banco de dados
├── docs/                # Documentação do projeto
└── frontend/            # Código do frontend em React`}
            </pre>
          </AccordionItem>

          <AccordionItem 
            title="Base de Dados" 
            isOpen={openSection === 2}
            onClick={() => toggleSection(2)}
          >
            <h3>Tabelas Principais</h3>
            <ul>
              <li>utilizadores: Armazena informações sobre médicos, administradores e leitores</li>
              <li>pacientes: Contém dados dos pacientes atendidos</li>
              <li>triagens: Regista as triagens realizadas com o Protocolo de Manchester</li>
              <li>doencas: Catálogo de doenças com suas características</li>
              <li>sintomas: Lista de sintomas para diagnóstico</li>
              <li>diagnostico: Regista os diagnósticos realizados pelos médicos</li>
              <li>protocolo_manchester: Regista as avaliações de prioridade</li>
            </ul>
          </AccordionItem>

          <AccordionItem 
            title="Estado Atual do Projeto" 
            isOpen={openSection === 3}
            onClick={() => toggleSection(3)}
          >
            <h3>Funcionalidades Implementadas</h3>
            <ul>
              <li>Base de dados normalizada criada e povoada</li>
              <li>Sistema de autenticação e autorização</li>
              <li>Módulos principais desenvolvidos (Triagem, Médico, Relatórios, Internamento)</li>
              <li>Sistema de notificações em tempo real</li>
              <li>Dashboard em tempo real para monitoramento</li>
              <li>Sistema de agendamento de consultas</li>
            </ul>

            <h3>Próximos Passos</h3>
            <ul>
              <li>Adicionar suporte para integração com dispositivos médicos</li>
              <li>Melhorar a acessibilidade da interface (WCAG 2.1)</li>
              <li>Implementar sistema de backup e restauração</li>
              <li>Adicionar suporte para múltiplos idiomas</li>
            </ul>
          </AccordionItem>

          <AccordionItem 
            title="Manutenção e Limpeza" 
            isOpen={openSection === 4}
            onClick={() => toggleSection(4)}
          >
            <h3>Arquivos para Limpeza</h3>
            <p>Scripts temporários que devem ser removidos antes do lançamento:</p>
            <ul>
              <li>backend/check_triagens_*.py</li>
              <li>backend/debug_routes.py</li>
              <li>backend/fix_syntax_error.py</li>
              <li>backend/update_*_service.py</li>
              <li>backend/verificar_internamentos.py</li>
            </ul>
          </AccordionItem>

          <AccordionItem 
            title="Documentação e Guias" 
            isOpen={openSection === 5}
            onClick={() => toggleSection(5)}
          >
            <h3>Documentos Principais</h3>
            <ul>
              <li>Arquitetura e Conceito: Visão geral, componentes, fluxos</li>
              <li>Processo e Workflow: Guias para desenvolvimento</li>
              <li>Documentação Técnica: Detalhes de implementação</li>
              <li>Guia do Utilizador: Manual de uso do sistema</li>
            </ul>
          </AccordionItem>

          <AccordionItem 
            title="Workflow e Processos" 
            isOpen={openSection === 6}
            onClick={() => toggleSection(6)}
          >
            <h3>Gestão de Documentação</h3>
            <ul>
              <li>Utilizar ficheiros markdown/MDX</li>
              <li>Manter os ficheiros com menos de 500 linhas</li>
              <li>Dividir em módulos quando necessário</li>
              <li>Documentar exclusivamente em português de Portugal</li>
            </ul>

            <h3>Comunicação e Contexto</h3>
            <ul>
              <li>Ser específico nos pedidos</li>
              <li>Fornecer contexto detalhado</li>
              <li>Manter histórico de decisões importantes</li>
            </ul>
          </AccordionItem>
        </div>
      </div>
    </div>
  );
};

export default Documents;
