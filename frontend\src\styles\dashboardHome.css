.dashboard-home {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  margin-bottom: 30px;
}

.dashboard-header h2 {
  font-size: 24px;
  color: #2c3e50;
  margin-bottom: 5px;
}

.dashboard-header p {
  color: #7f8c8d;
  font-size: 16px;
}

/* Cards de estatísticas */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.stat-card h3 {
  font-size: 16px;
  color: #7f8c8d;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-today {
  font-size: 14px;
  color: #3498db;
}

/* Ações rápidas */
.quick-actions {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.quick-actions h3 {
  font-size: 18px;
  color: #2c3e50;
  margin-bottom: 15px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.action-buttons button {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.action-buttons button:hover {
  background: #2980b9;
}

/* Tabelas */
.pending-screenings,
.upcoming-appointments {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.pending-screenings h3,
.upcoming-appointments h3 {
  font-size: 18px;
  color: #2c3e50;
  margin-bottom: 15px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

table th,
table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

table th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
}

table tr:hover {
  background-color: #f8f9fa;
}

table button {
  background: #3498db;
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s ease;
}

table button:hover {
  background: #2980b9;
}

/* Prioridades */
.prioridade-vermelho {
  color: #e74c3c;
  font-weight: bold;
}

.prioridade-laranja {
  color: #e67e22;
  font-weight: bold;
}

.prioridade-amarelo {
  color: #f1c40f;
  font-weight: bold;
}

.prioridade-verde {
  color: #2ecc71;
  font-weight: bold;
}

.prioridade-azul {
  color: #3498db;
  font-weight: bold;
}

/* Gráficos */
.dashboard-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.dashboard-charts .chart-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  height: 350px;
  width: 100%;
  position: relative;
}

.dashboard-charts .chart-container h3 {
  font-size: 18px;
  color: #2c3e50;
  margin-bottom: 15px;
  text-align: center;
}

/* Ajuste para o conteúdo do gráfico */
.dashboard-charts .chart-container canvas {
  width: 100% !important;
  height: calc(100% - 40px) !important;
  max-height: 300px;
}

/* Loading */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 18px;
  color: #7f8c8d;
}

/* Responsividade */
@media (max-width: 1200px) {
  .dashboard-charts {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .dashboard-charts {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons button {
    width: 100%;
  }

  .dashboard-charts .chart-container {
    height: 300px;
  }
}

@media (max-width: 480px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }

  .dashboard-charts .chart-container {
    height: 250px;
  }
}
