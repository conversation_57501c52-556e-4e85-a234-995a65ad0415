.dashboard-realtime-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: "Roboto", sans-serif;
}

/* Estilo para o container de notificações */
.notifications-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 350px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dashboard-header h1 {
  color: #16808c;
  margin: 0;
  font-size: 28px;
}

/* Estilo para a seção de informação sobre dados */
.dados-info-container {
  margin-bottom: 20px;
  width: 100%;
}

.dados-teste-badge {
  display: inline-block;
  background-color: #f8d7da;
  color: #721c24;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-left: 10px;
  border: 1px solid #f5c6cb;
}

.dados-info {
  background-color: #f8f9fa;
  border-left: 4px solid #17a2b8;
  padding: 12px 15px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dados-info i {
  color: #17a2b8;
  font-size: 18px;
  margin-right: 10px;
}

.dados-info span {
  color: #495057;
  font-size: 14px;
  line-height: 1.5;
}

.dashboard-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.periodo-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  background-color: #f5f5f5;
  border-radius: 5px;
  padding: 3px;
}

.periodo-btn {
  background: none;
  border: none;
  padding: 8px 15px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.periodo-btn.active {
  background-color: #16808c;
  color: white;
}

/* Estilos para o seletor de data personalizado */
.date-picker-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
  padding: 10px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  width: 100%;
}

.date-picker-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.date-picker-item label {
  font-size: 14px;
  color: #666;
}

.date-picker {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  width: 120px;
}

.btn-aplicar-datas {
  background-color: #16808c;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.btn-aplicar-datas:hover {
  background-color: #126b75;
}

.refresh-controls {
  display: flex;
  gap: 10px;
}

.btn-refresh,
.btn-auto-refresh {
  background: none;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  transition: all 0.3s;
}

.btn-refresh:hover,
.btn-auto-refresh:hover {
  background-color: #f0f0f0;
  color: #16808c;
}

.btn-auto-refresh.active {
  background-color: #16808c;
  color: white;
}

.loading {
  text-align: center;
  padding: 50px;
  font-size: 18px;
  color: #666;
}

/* Cards de Estatísticas */
.estatisticas-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.estatistica-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  align-items: center;
  transition: transform 0.3s, box-shadow 0.3s;
}

.estatistica-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.estatistica-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(22, 128, 140, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
  color: #16808c;
}

.estatistica-info {
  flex: 1;
}

.estatistica-info h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
  color: #666;
}

.estatistica-valor {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.estatistica-subvalor {
  font-size: 14px;
  color: #666;
}

.highlight {
  color: #498c6d;
  font-weight: bold;
}

/* Gráficos */
/* Estilos específicos para os gráficos no dashboard em tempo real */
.dashboard-graficos {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 25px;
  margin-top: 25px;
}

.grafico-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 25px;
  position: relative;
  height: auto;
  min-height: 400px; /* Aumentar altura mínima */
}

.grafico-container h3 {
  font-size: 18px;
  color: #2c3e50;
  margin-bottom: 15px;
  text-align: center;
  position: relative;
}

.grafico-container canvas {
  width: 100% !important;
  height: 300px !important;
}

/* Remover qualquer estilo que possa estar interferindo */
.chart-wrapper {
  display: block;
  width: 100%;
  height: auto;
}

/* Estilo específico para containers de gráficos de pizza */
.grafico-container:has(.pie-chart-container) {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 25px;
}

/* Estilo para o wrapper do gráfico de pizza */
.pie-chart-container {
  width: 100%;
  max-width: 320px; /* Tamanho máximo controlado */
  height: 320px; /* Altura igual à largura máxima para garantir proporção 1:1 */
  margin: 0 auto;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-chart-container canvas {
  max-width: 100%;
  max-height: 100%;
}

/* Ajuste para o container de gráficos que contém gráficos de pizza */
.dashboard-graficos .grafico-container:has(canvas[data-type="pie"]) {
  padding: 15px;
  display: flex;
  flex-direction: column;
}

/* Melhorar a visualização em telas menores */
@media (max-width: 768px) {
  .grafico-container canvas[data-type="pie"],
  .grafico-container canvas[data-type="doughnut"] {
    max-height: 220px !important;
  }
}

/* Tempos de Espera */
.tempos-espera-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.tempo-espera-card {
  border-radius: 8px;
  border: 1px solid;
  padding: 15px;
  transition: transform 0.3s, box-shadow 0.3s;
}

.tempo-espera-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.tempo-espera-titulo {
  margin: 0 0 10px 0;
  font-size: 16px;
  text-align: center;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.tempo-espera-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tempo-stat {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.tempo-label {
  color: #666;
}

.tempo-valor {
  font-weight: bold;
  color: #333;
}

/* Atividades Recentes */
.atividades-recentes {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.atividade-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.atividade-container h3 {
  color: #16808c;
  margin: 0 0 15px 0;
  font-size: 18px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.atividade-lista {
  margin-bottom: 15px;
}

.atividade-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
  transition: background-color 0.3s;
}

.atividade-item:last-child {
  border-bottom: none;
}

.atividade-item:hover {
  background-color: #f9f9f9;
}

.atividade-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(22, 128, 140, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
  color: #16808c;
}

.atividade-info {
  flex: 1;
}

.atividade-titulo {
  font-weight: bold;
  color: #333;
  margin-bottom: 3px;
}

.atividade-subtitulo {
  font-size: 12px;
  color: #666;
}

.btn-ver-detalhes {
  background: none;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #16808c;
  transition: background-color 0.3s;
}

.btn-ver-detalhes:hover {
  background-color: rgba(22, 128, 140, 0.1);
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
  background-color: #f9f9f9;
  border-radius: 5px;
}

.btn-ver-todos {
  display: block;
  width: 100%;
  padding: 10px;
  text-align: center;
  background-color: #f5f5f5;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  color: #16808c;
  font-weight: bold;
  transition: background-color 0.3s;
}

.btn-ver-todos:hover {
  background-color: #e9e9e9;
}

/* Responsividade */
@media (max-width: 1024px) {
  .estatisticas-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .dashboard-graficos {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .dashboard-actions {
    width: 100%;
    justify-content: space-between;
  }

  .atividades-recentes {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .estatisticas-cards {
    grid-template-columns: 1fr;
  }

  .periodo-selector {
    width: 100%;
    justify-content: space-between;
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .dashboard-graficos {
    grid-template-columns: 1fr;
  }
  
  .grafico-container {
    min-height: 350px;
  }
  
  .pie-chart-container {
    max-width: 280px;
    height: 280px;
  }
}
