.page-container {
  padding: 20px;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.documents-container {
  max-width: 1200px;
  margin: 0 auto;
  background-color: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.documents-header {
  margin-bottom: 2rem;
  text-align: center;
}

.documents-header h1 {
  color: #16808C;
  font-size: 2rem;
  margin-bottom: 1rem;
}

/* Custom Accordion Styles */
.accordion {
  width: 100%;
}

.accordion-item {
  margin-bottom: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.accordion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #fff;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.accordion-header:hover {
  background-color: #f5f5f5;
}

.accordion-header.active {
  background-color: #16808C;
}

.accordion-header.active h2 {
  color: white;
}

.accordion-header h2 {
  margin: 0;
  font-size: 1.2rem;
  color: #16808C;
}

.accordion-icon {
  font-size: 1.5rem;
  color: #16808C;
}

.accordion-header.active .accordion-icon {
  color: white;
}

.accordion-content {
  padding: 1.5rem;
  background-color: white;
}

.accordion-content h3 {
  color: #16808C;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.accordion-content p {
  color: #333;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.accordion-content ul {
  list-style-type: none;
  padding-left: 0;
}

.accordion-content ul li {
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.accordion-content ul li:last-child {
  border-bottom: none;
}

/* Animation for accordion */
.accordion-content {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
