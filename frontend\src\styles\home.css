
:root {
  --primary-color: #16808C; /* Azul esverdeado forte */
  --secondary-color: #498C6D; /* Verde escuro */
  --accent-color: #69BFA7; /* Verde claro */
  --hover-color: #A3D9C9; /* Efeito hover */
  --background-color: #f5f7fa;
  --text-color: #2d3748;
  --white: #FFFFFF;
  --success-color: #28a745;
  --warning-color: #ECC94B;
  --danger-color: #dc3545;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

.home-container {
  overflow-x: hidden;
}

/* Hero Section with Slideshow */
.hero-section {
  position: relative;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.slideshow-container {
  position: relative;
  height: 100%;
  width: 100%;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1s ease-in-out;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.slide::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.6) 100%
  );
}

.slide.active {
  opacity: 1;
  z-index: 1;
}

.hero-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  width: 90%;
  max-width: 1000px;
  padding: 2rem;
  text-align: center;
  color: var(--white);
  background: rgba(0, 0, 0, 0.3);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.hero-content h1 {
  font-size: 4.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 1px;
}

.hero-content h2 {
  font-size: 2.5rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  color: var(--accent-color);
}

.hero-content p {
  font-size: 1.4rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Slide Navigation Dots */
.slide-dots {
  position: absolute;
  bottom: 1.5rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.8rem;
  z-index: 3;
  padding: 0.5rem;
}

.slide-dot {
  width: 8px;
  height: 8px; /* Exatamente igual à largura */
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  padding: 0; /* Remove qualquer padding que possa afetar a forma */
  margin: 0; /* Remove qualquer margem que possa afetar a forma */
}

.slide-dot.active {
  background: var(--white);
  transform: scale(1.1);
}

.slide-dot:hover {
  background: var(--white);
  transform: scale(1.1);
}

/* Animation for slide transitions */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

/* Buttons */
.primary-button {
  background: var(--primary-color);
  color: var(--white);
  padding: 1rem 2rem;
  border: none;
  border-radius: 30px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.primary-button:hover {
  background: var(--hover-color);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.secondary-button {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  padding: 1rem 2rem;
  border-radius: 30px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  margin-left: 1rem;
}

.secondary-button:hover {
  background: var(--primary-color);
  color: var(--white);
}

/* Features Section */
.features-section {
  padding: 5rem 2rem;
  background: var(--light-bg);
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 3rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: var(--white);
  padding: 2rem;
  border-radius: 15px;
  box-shadow: var(--box-shadow);
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.feature-icon {
  font-size: 2.5rem;
  color: var(--secondary-color);
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: scale(1.2);
  color: var(--accent-color);
}

.feature-card h3 {
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
}

.feature-card:hover h3 {
  color: var(--accent-color);
}

.feature-card p {
  transition: all 0.3s ease;
  opacity: 0.8;
}

.feature-card:hover p {
  opacity: 1;
}

/* Efeito de brilho ao passar o mouse */
.feature-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 80%
  );
  transform: rotate(45deg);
  transition: 0.5s;
  opacity: 0;
}

.feature-card:hover::after {
  opacity: 1;
  transform: rotate(45deg) translate(50%, 50%);
}

/* Statistics Section */
.statistics-section {
  background: var(--primary-color);
  color: var(--white);
  padding: 4rem 2rem;
}

.stat-container {
  display: flex;
  justify-content: space-around;
  max-width: 1200px;
  margin: 0 auto;
}

.stat-item {
  text-align: center;
}

.stat-item h3 {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

/* Workflow Section */
.workflow-section {
  padding: 5rem 2rem;
  background: var(--white);
}

.workflow-steps {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.step {
  position: relative;
  text-align: center;
  padding: 2rem;
  transition: all 0.3s ease;
  border-radius: 15px;
  background: var(--white);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.step:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* Cores base para os números centralizados */
.step:nth-child(1) .step-number {
  background: var(--primary-color);
}

.step:nth-child(2) .step-number {
  background: var(--secondary-color);
}

.step:nth-child(3) .step-number {
  background: var(--accent-color);
}

.step:nth-child(4) .step-number {
  background: #7B68EE;
}

/* Efeito hover com cores correspondentes mais claras */
.step:nth-child(1):hover .step-number {
  background: var(--hover-color);
  box-shadow: 0 0 15px rgba(22, 128, 140, 0.5);
}

.step:nth-child(2):hover .step-number {
  background: #6AB88F;
  box-shadow: 0 0 15px rgba(73, 140, 109, 0.5);
}

.step:nth-child(3):hover .step-number {
  background: #8ED3BE;
  box-shadow: 0 0 15px rgba(105, 191, 167, 0.5);
}

.step:nth-child(4):hover .step-number {
  background: #9B8EF3;
  box-shadow: 0 0 15px rgba(123, 104, 238, 0.5);
}

.step-number {
  width: 40px;
  height: 40px;
  color: var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-weight: bold;
  transition: all 0.5s ease;
  background: var(--primary-color);
}

@keyframes numberPulse {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.2);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

.step:hover .step-number {
  animation: numberPulse 1.5s infinite;
}

.step h3 {
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
}

.step:hover h3 {
  color: var(--accent-color);
}

.step p {
  transition: all 0.3s ease;
  opacity: 0.8;
}

.step:hover p {
  opacity: 1;
}

/* Efeito de brilho ao passar o mouse */
.step::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 80%
  );
  transform: rotate(45deg);
  transition: 0.5s;
  opacity: 0;
}

.step:hover::after {
  opacity: 1;
  transform: rotate(45deg) translate(50%, 50%);
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
  color: var(--white);
  padding: 5rem 2rem;
  text-align: center;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
}

.cta-buttons {
  margin-top: 2rem;
}

/* Footer */
.footer {
  background: var(--text-color);
  color: var(--white);
  padding: 4rem 2rem 2rem;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.footer-section h3 {
  margin-bottom: 1rem;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: 0.5rem;
  cursor: pointer;
}

.footer-bottom {
  text-align: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Benefits Section */
.benefits-section {
  padding: 5rem 2rem;
  background: var(--background-color);
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.benefit-card {
  background: var(--white);
  padding: 2rem;
  border-radius: 15px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  text-align: center;
}

.benefit-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.benefit-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.benefit-card h3 {
  color: var(--text-color);
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.benefit-card p {
  color: var(--text-color);
  opacity: 0.8;
  line-height: 1.6;
}

/* How It Works Section - Estilo atualizado */
.how-it-works-section {
  padding: 5rem 2rem;
  background: var(--background-color);
}

.workflow-container {
  max-width: 1200px;
  margin: 0 auto;
}

.workflow-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.workflow-card {
  background: var(--white);
  padding: 2rem;
  border-radius: 15px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.workflow-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.workflow-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 2;
}

.workflow-card h3 {
  color: var(--text-color);
  margin-bottom: 1rem;
  font-size: 1.5rem;
  position: relative;
  z-index: 2;
}

.workflow-card p {
  color: var(--text-color);
  opacity: 0.8;
  line-height: 1.6;
  position: relative;
  z-index: 2;
}

/* Efeito de brilho ao passar o mouse */
.workflow-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 80%
  );
  transform: rotate(45deg);
  transition: 0.5s;
  opacity: 0;
}

.workflow-card:hover::after {
  opacity: 1;
  transform: rotate(45deg) translate(50%, 50%);
}

/* More Information Section */
.more-info-section {
  padding: 5rem 2rem;
  background: var(--background-gradient);
}

.info-container {
  max-width: 1200px;
  margin: 0 auto;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-top: 3rem;
}

.info-card {
  background: var(--white);
  padding: 2rem;
  border-radius: 15px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.info-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.info-icon {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  text-align: center;
}

.info-card h3 {
  color: var(--text-color);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  text-align: center;
}

.info-list {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
  flex-grow: 1;
}

.info-list li {
  padding: 0.5rem 0;
  color: var(--text-color);
  position: relative;
  padding-left: 1.5rem;
  line-height: 1.4;
}

.info-list li::before {
  content: '→';
  position: absolute;
  left: 0;
  color: var(--primary-color);
}

.info-button {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  width: 100%;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
}

.info-button:hover {
  background: var(--hover-color);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

/* Efeito de gradiente no hover */
.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  opacity: 0;
  transition: var(--transition);
}

.info-card:hover::before {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .info-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 3rem;
  }

  .hero-content h2 {
    font-size: 1.5rem;
  }

  .stat-container {
    flex-direction: column;
    gap: 2rem;
  }

  .cta-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .secondary-button {
    margin-left: 0;
  }

  .workflow-step {
    flex-direction: column;
    text-align: center;
  }

  .step-icon {
    margin: 0 0 1rem 0;
  }

  .workflow-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .workflow-card {
    padding: 1.5rem;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .info-card {
    padding: 1.5rem;
  }
  
  .info-button {
    padding: 0.7rem 1.2rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 1024px) {
  .hero-content h1 {
    font-size: 3.5rem;
  }

  .hero-content h2 {
    font-size: 2rem;
  }

  .hero-content p {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .hero-content {
    width: 95%;
    padding: 1.5rem;
  }

  .hero-content h1 {
    font-size: 2.8rem;
  }

  .hero-content h2 {
    font-size: 1.8rem;
  }

  .hero-content p {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .hero-content h1 {
    font-size: 2.2rem;
  }

  .hero-content h2 {
    font-size: 1.4rem;
  }

  .hero-content p {
    font-size: 1rem;
  }
}
