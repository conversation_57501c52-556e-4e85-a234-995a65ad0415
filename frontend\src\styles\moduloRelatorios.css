.modulo-relatorios-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Roboto', sans-serif;
}

.modulo-relatorios-container h1 {
  color: #16808C;
  margin-bottom: 20px;
  font-size: 28px;
  border-bottom: 2px solid #16808C;
  padding-bottom: 10px;
}

.relatorios-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filtros {
  display: flex;
  gap: 15px;
}

.filtro {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filtro label {
  font-weight: bold;
  color: #333;
}

.filtro select {
  padding: 8px;
  border-radius: 5px;
  border: 1px solid #ddd;
  background-color: white;
}

.acoes {
  display: flex;
  gap: 10px;
}

.btn-exportar {
  background-color: #498C6D;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.3s;
}

.btn-exportar:hover {
  background-color: #3d7a5d;
}

.loading {
  text-align: center;
  padding: 50px;
  font-size: 18px;
  color: #666;
}

/* Estatísticas Gerais */
.estatisticas-gerais {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.estatistica-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  text-align: center;
}

.estatistica-card h3 {
  color: #16808C;
  margin-bottom: 10px;
  font-size: 18px;
}

.estatistica-valor {
  font-size: 32px;
  font-weight: bold;
  color: #498C6D;
}

/* Relatório Container */
.relatorio-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.relatorio-header {
  margin-bottom: 30px;
}

.relatorio-header h2 {
  color: #16808C;
  margin-bottom: 10px;
  font-size: 24px;
}

.relatorio-header p {
  color: #666;
  margin-bottom: 5px;
}

/* Gráficos */
.relatorio-graficos {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.grafico-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #eee;
}

.grafico-container h3 {
  color: #16808C;
  margin-bottom: 15px;
  font-size: 18px;
  text-align: center;
}

/* Gráfico de Barras */
.grafico-barras {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  height: 250px;
  padding-top: 20px;
}

.barra-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 60px;
}

.barra {
  width: 40px;
  background-color: #16808C;
  border-radius: 4px 4px 0 0;
  transition: height 0.5s;
}

.barra-label {
  margin-top: 10px;
  font-size: 14px;
  color: #333;
  text-align: center;
}

.barra-valor {
  margin-top: 5px;
  font-size: 12px;
  color: #666;
}

/* Gráfico de Pizza */
.grafico-pizza-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.grafico-pizza {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background-color: #f0f0f0;
  position: relative;
  overflow: hidden;
}

.pizza-fatia {
  position: absolute;
  width: 100%;
  height: 100%;
  transform-origin: center;
}

.grafico-pizza-legenda {
  flex: 1;
  margin-left: 30px;
}

.legenda-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.legenda-cor {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  margin-right: 10px;
}

.legenda-label {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.legenda-valor {
  font-size: 14px;
  color: #666;
  font-weight: bold;
}

/* Tabela */
.relatorio-tabela {
  margin-top: 30px;
}

.relatorio-tabela h3 {
  color: #16808C;
  margin-bottom: 15px;
  font-size: 18px;
}

.tabela-container {
  overflow-x: auto;
}

.tabela-container table {
  width: 100%;
  border-collapse: collapse;
}

.tabela-container th {
  background-color: #f5f5f5;
  padding: 12px 15px;
  text-align: left;
  font-weight: bold;
  color: #333;
  border-bottom: 2px solid #ddd;
}

.tabela-container td {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
}

.tabela-container tr:last-child td {
  border-bottom: none;
}

.tabela-container tr:hover {
  background-color: #f9f9f9;
}

/* Badges de Prioridade */
.prioridade-badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 15px;
  color: white;
  font-weight: bold;
  font-size: 12px;
  text-align: center;
}

.prioridade-badge.vermelho {
  background-color: #FF0000;
}

.prioridade-badge.laranja {
  background-color: #FF8000;
}

.prioridade-badge.amarelo {
  background-color: #FFD700;
  color: #333;
}

.prioridade-badge.verde {
  background-color: #008000;
}

.prioridade-badge.azul {
  background-color: #0000FF;
}

/* Responsividade */
@media (max-width: 768px) {
  .estatisticas-gerais {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .relatorio-graficos {
    grid-template-columns: 1fr;
  }
  
  .filtros {
    flex-direction: column;
    gap: 10px;
  }
  
  .relatorios-actions {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .acoes {
    width: 100%;
    justify-content: space-between;
  }
}

/* Ajustes específicos para gráficos de pizza nos relatórios */
.relatorio-graficos .grafico-container:has(canvas[data-type="pie"]),
.relatorio-graficos .grafico-container:has(canvas[data-type="doughnut"]) {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.relatorio-graficos canvas[data-type="pie"],
.relatorio-graficos canvas[data-type="doughnut"] {
  max-width: 90% !important;
  max-height: 280px !important;
  margin: 0 auto;
}
